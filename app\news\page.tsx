import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Phone, Mail, MapPin, Factory, Search, Calendar, User, ArrowRight } from "lucide-react"
import Link from "next/link"

const newsArticles = [
  {
    id: 1,
    title: "Новое поступление концевых фрез с покрытием TiAlN",
    slug: "new-end-mills-tialn-coating",
    category: "Новинки",
    date: "15 декабря 2024",
    author: "Елена Петрова",
    excerpt:
      "В наш ассортимент поступила новая партия высокоточных концевых фрез с инновационным покрытием TiAlN, обеспечивающим увеличенный срок службы и повышенную производительность.",
    content:
      "Мы рады сообщить о поступлении новой партии концевых фрез с покрытием TiAlN от ведущих производителей. Данное покрытие обеспечивает...",
    image: "new%20end%20mills%20tialn%20coating%20delivery",
    readTime: "3 мин",
    featured: true,
  },
  {
    id: 2,
    title: "Участие в выставке Металлообработка-2024",
    slug: "metalworking-exhibition-2024",
    category: "События",
    date: "10 декабря 2024",
    author: "Александр Новиков",
    excerpt:
      "Приглашаем посетить наш стенд на международной выставке металлообработки, где мы представим новейшие разработки и технологии производства режущего инструмента.",
    content: "С 15 по 18 января 2025 года в Минске пройдет крупнейшая выставка металлообработки в регионе...",
    image: "metalworking%20exhibition%202024%20booth",
    readTime: "5 мин",
    featured: true,
  },
  {
    id: 3,
    title: "Расширение производственных мощностей",
    slug: "production-expansion-2024",
    category: "Новости компании",
    date: "5 декабря 2024",
    author: "Михаил Сидоров",
    excerpt:
      "Запуск нового участка по восстановлению сложного режущего инструмента позволит увеличить объемы производства и сократить сроки выполнения заказов.",
    content:
      "В рамках программы модернизации производства мы запустили новый участок, оснащенный современным оборудованием...",
    image: "production%20expansion%20new%20equipment",
    readTime: "4 мин",
    featured: false,
  },
  {
    id: 4,
    title: "Советы по выбору концевых фрез для алюминия",
    slug: "end-mills-aluminum-selection-tips",
    category: "Советы мастеру",
    date: "28 ноября 2024",
    author: "Елена Петрова",
    excerpt:
      "Обработка алюминия требует особого подхода к выбору режущего инструмента. Рассказываем о ключевых параметрах и рекомендациях.",
    content:
      "Алюминий - один из самых распространенных материалов в современном машиностроении. Для его эффективной обработки...",
    image: "aluminum%20machining%20end%20mills%20tips",
    readTime: "6 мин",
    featured: false,
  },
  {
    id: 5,
    title: "Новые технологии покрытий режущего инструмента",
    slug: "new-coating-technologies-2024",
    category: "Технологии",
    date: "20 ноября 2024",
    author: "Ольга Козлова",
    excerpt:
      "Обзор современных технологий нанесения покрытий на режущий инструмент и их влияние на производительность обработки.",
    content:
      "Развитие технологий покрытий режущего инструмента не стоит на месте. В 2024 году появились новые решения...",
    image: "cutting%20tool%20coating%20technologies",
    readTime: "8 мин",
    featured: false,
  },
  {
    id: 6,
    title: "Правильная заточка сверл: пошаговое руководство",
    slug: "drill-sharpening-guide",
    category: "Советы мастеру",
    date: "15 ноября 2024",
    author: "Михаил Сидоров",
    excerpt:
      "Подробное руководство по правильной заточке спиральных сверл с иллюстрациями и практическими советами от наших экспертов.",
    content: "Правильная заточка сверл - залог качественной обработки отверстий. В этом руководстве мы расскажем...",
    image: "drill%20sharpening%20step%20by%20step%20guide",
    readTime: "7 мин",
    featured: false,
  },
]

const categories = ["Все категории", "Новости компании", "Новинки", "События", "Советы мастеру", "Технологии"]

export default function NewsPage() {
  const featuredArticles = newsArticles.filter((article) => article.featured)
  const regularArticles = newsArticles.filter((article) => !article.featured)

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Factory className="h-8 w-8 text-accent" />
            <span className="text-2xl font-bold font-heading">ANAJ.BY</span>
          </Link>

          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/" className="text-sm font-medium hover:text-accent transition-colors">
              Главная
            </Link>
            <Link href="/catalog" className="text-sm font-medium hover:text-accent transition-colors">
              Каталог
            </Link>
            <Link href="/services" className="text-sm font-medium hover:text-accent transition-colors">
              Услуги
            </Link>
            <Link href="/about" className="text-sm font-medium hover:text-accent transition-colors">
              О компании
            </Link>
            <Link href="/news" className="text-sm font-medium text-accent">
              Новости
            </Link>
            <Link href="/contacts" className="text-sm font-medium hover:text-accent transition-colors">
              Контакты
            </Link>
          </nav>

          <Button className="bg-accent hover:bg-accent/90">
            <Phone className="mr-2 h-4 w-4" />
            Заказать звонок
          </Button>
        </div>
      </header>

      {/* Page Header */}
      <section className="py-12 bg-secondary/30">
        <div className="container">
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold font-heading">Новости и статьи</h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Последние новости компании, обзоры продукции и полезные советы от экспертов по металлообработке
            </p>
          </div>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="py-8 border-b">
        <div className="container">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 w-full lg:w-auto">
              <div className="relative flex-1 lg:w-80">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input placeholder="Поиск по новостям..." className="pl-10" />
              </div>

              <Select defaultValue="Все категории">
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Articles */}
      {featuredArticles.length > 0 && (
        <section className="py-12">
          <div className="container">
            <h2 className="text-2xl font-bold font-heading mb-8">Рекомендуемые статьи</h2>
            <div className="grid lg:grid-cols-2 gap-8">
              {featuredArticles.map((article) => (
                <Card key={article.id} className="hover:shadow-lg transition-shadow group overflow-hidden">
                  <div className="aspect-video overflow-hidden">
                    <img
                      src={`/abstract-geometric-shapes.png?height=300&width=600&query=${article.image}`}
                      alt={article.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="secondary">{article.category}</Badge>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4 mr-1" />
                        {article.date}
                      </div>
                    </div>
                    <CardTitle className="font-heading group-hover:text-accent transition-colors">
                      <Link href={`/news/${article.slug}`}>{article.title}</Link>
                    </CardTitle>
                    <CardDescription className="line-clamp-3">{article.excerpt}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <User className="h-4 w-4 mr-1" />
                        {article.author}
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-muted-foreground">{article.readTime}</span>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/news/${article.slug}`}>
                            Читать далее
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* All Articles */}
      <section className="py-12 bg-secondary/30">
        <div className="container">
          <h2 className="text-2xl font-bold font-heading mb-8">Все новости</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {regularArticles.map((article) => (
              <Card key={article.id} className="hover:shadow-lg transition-shadow group">
                <div className="aspect-video overflow-hidden rounded-t-lg">
                  <img
                    src={`/abstract-geometric-shapes.png?height=200&width=400&query=${article.image}`}
                    alt={article.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline" className="text-xs">
                      {article.category}
                    </Badge>
                    <span className="text-xs text-muted-foreground">{article.readTime}</span>
                  </div>
                  <CardTitle className="font-heading text-lg leading-tight group-hover:text-accent transition-colors">
                    <Link href={`/news/${article.slug}`}>{article.title}</Link>
                  </CardTitle>
                  <CardDescription className="text-sm line-clamp-2">{article.excerpt}</CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                    <div className="flex items-center">
                      <User className="h-3 w-3 mr-1" />
                      {article.author}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      {article.date}
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="w-full bg-transparent" asChild>
                    <Link href={`/news/${article.slug}`}>Читать полностью</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Показать еще статьи
            </Button>
          </div>
        </div>
      </section>

      {/* Newsletter Subscription */}
      <section className="py-16">
        <div className="container">
          <div className="max-w-2xl mx-auto text-center space-y-6">
            <h2 className="text-3xl font-bold font-heading">Подписка на новости</h2>
            <p className="text-muted-foreground">
              Получайте последние новости, обзоры продукции и полезные советы по металлообработке на вашу почту
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input placeholder="Ваш email" type="email" className="flex-1" />
              <Button className="bg-accent hover:bg-accent/90">Подписаться</Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Нажимая кнопку, вы соглашаетесь с обработкой персональных данных
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-primary text-primary-foreground py-12">
        <div className="container">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Factory className="h-6 w-6 text-accent" />
                <span className="text-xl font-bold font-heading">ANAJ.BY</span>
              </div>
              <p className="text-sm opacity-80">
                Производство и восстановление режущего инструмента высочайшего качества
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Контакты</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-accent" />
                  <span>+375 (29) 123-45-67</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-accent" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-accent" />
                  <span>г. Минск, ул. Промышленная, 15</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Услуги</h4>
              <div className="space-y-2 text-sm">
                <Link href="/services/manufacturing" className="block hover:text-accent transition-colors">
                  Производство инструмента
                </Link>
                <Link href="/services/restoration" className="block hover:text-accent transition-colors">
                  Восстановление инструмента
                </Link>
                <Link href="/services/custom" className="block hover:text-accent transition-colors">
                  Индивидуальные заказы
                </Link>
                <Link href="/services/consulting" className="block hover:text-accent transition-colors">
                  Консультации
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Информация</h4>
              <div className="space-y-2 text-sm">
                <Link href="/about" className="block hover:text-accent transition-colors">
                  О компании
                </Link>
                <Link href="/catalog" className="block hover:text-accent transition-colors">
                  Каталог продукции
                </Link>
                <Link href="/news" className="block hover:text-accent transition-colors">
                  Новости
                </Link>
                <Link href="/contacts" className="block hover:text-accent transition-colors">
                  Контакты
                </Link>
              </div>
            </div>
          </div>

          <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm opacity-80">
            <p>&copy; 2024 ANAJ.BY. Все права защищены.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
