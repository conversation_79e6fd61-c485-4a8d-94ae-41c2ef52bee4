import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Phone, Mail, MapPin, Factory, ArrowLeft, Calendar, User, Clock, Share2, Tag } from "lucide-react"
import Link from "next/link"
import { notFound } from "next/navigation"

const newsArticles = [
  {
    id: 1,
    title: "Новое поступление концевых фрез с покрытием TiAlN",
    slug: "new-end-mills-tialn-coating",
    category: "Новинки",
    date: "15 декабря 2024",
    author: "Елена Петрова",
    excerpt:
      "В наш ассортимент поступила новая партия высокоточных концевых фрез с инновационным покрытием TiAlN, обеспечивающим увеличенный срок службы и повышенную производительность.",
    content: `
      <p>Мы рады сообщить о поступлении новой партии концевых фрез с покрытием TiAlN от ведущих производителей. Данное покрытие обеспечивает значительное увеличение срока службы инструмента и повышение производительности обработки.</p>

      <h3>Преимущества покрытия TiAlN</h3>
      <p>Покрытие TiAlN (нитрид титана-алюминия) представляет собой одно из самых современных решений в области защиты режущего инструмента. Основные преимущества:</p>
      <ul>
        <li>Увеличение срока службы инструмента до 300%</li>
        <li>Повышение скорости резания на 25-40%</li>
        <li>Улучшенная стойкость к высоким температурам</li>
        <li>Снижение коэффициента трения</li>
        <li>Универсальность применения для различных материалов</li>
      </ul>

      <h3>Технические характеристики</h3>
      <p>Новые концевые фрезы доступны в диаметрах от 1 до 20 мм с различными геометриями режущей части. Покрытие TiAlN наносится методом PVD (физическое осаждение из паровой фазы), что обеспечивает равномерность и высокую адгезию к основе.</p>

      <h3>Области применения</h3>
      <p>Фрезы с покрытием TiAlN особенно эффективны при обработке:</p>
      <ul>
        <li>Нержавеющих сталей</li>
        <li>Жаропрочных сплавов</li>
        <li>Титановых сплавов</li>
        <li>Закаленных сталей</li>
        <li>Чугуна</li>
      </ul>

      <p>Новые фрезы уже доступны для заказа. Для получения подробной информации и консультации по выбору оптимального инструмента обращайтесь к нашим специалистам.</p>
    `,
    image: "new%20end%20mills%20tialn%20coating%20detailed",
    readTime: "3 мин",
    featured: true,
    tags: ["Новинки", "Концевые фрезы", "Покрытия", "TiAlN"],
  },
]

interface ArticlePageProps {
  params: {
    slug: string
  }
}

export default function ArticlePage({ params }: ArticlePageProps) {
  const article = newsArticles.find((a) => a.slug === params.slug)

  if (!article) {
    notFound()
  }

  const relatedArticles = newsArticles.filter((a) => a.id !== article.id).slice(0, 3)

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Factory className="h-8 w-8 text-accent" />
            <span className="text-2xl font-bold font-heading">ANAJ.BY</span>
          </Link>

          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/" className="text-sm font-medium hover:text-accent transition-colors">
              Главная
            </Link>
            <Link href="/catalog" className="text-sm font-medium hover:text-accent transition-colors">
              Каталог
            </Link>
            <Link href="/services" className="text-sm font-medium hover:text-accent transition-colors">
              Услуги
            </Link>
            <Link href="/about" className="text-sm font-medium hover:text-accent transition-colors">
              О компании
            </Link>
            <Link href="/news" className="text-sm font-medium text-accent">
              Новости
            </Link>
            <Link href="/contacts" className="text-sm font-medium hover:text-accent transition-colors">
              Контакты
            </Link>
          </nav>

          <Button className="bg-accent hover:bg-accent/90">
            <Phone className="mr-2 h-4 w-4" />
            Заказать звонок
          </Button>
        </div>
      </header>

      {/* Breadcrumbs */}
      <section className="py-4 border-b">
        <div className="container">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Link href="/" className="hover:text-accent transition-colors">
              Главная
            </Link>
            <span>/</span>
            <Link href="/news" className="hover:text-accent transition-colors">
              Новости
            </Link>
            <span>/</span>
            <span className="text-foreground">{article.title}</span>
          </div>
        </div>
      </section>

      {/* Article */}
      <article className="py-12">
        <div className="container">
          <div className="mb-6">
            <Button variant="outline" asChild>
              <Link href="/news">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Назад к новостям
              </Link>
            </Button>
          </div>

          <div className="max-w-4xl mx-auto">
            {/* Article Header */}
            <header className="space-y-6 mb-8">
              <div className="flex items-center gap-4 flex-wrap">
                <Badge variant="secondary">{article.category}</Badge>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4 mr-1" />
                  {article.date}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Clock className="h-4 w-4 mr-1" />
                  {article.readTime}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <User className="h-4 w-4 mr-1" />
                  {article.author}
                </div>
              </div>

              <h1 className="text-4xl font-bold font-heading leading-tight">{article.title}</h1>

              <p className="text-xl text-muted-foreground leading-relaxed">{article.excerpt}</p>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Tag className="h-4 w-4 text-muted-foreground" />
                  <div className="flex gap-2">
                    {article.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Поделиться
                </Button>
              </div>
            </header>

            {/* Featured Image */}
            <div className="aspect-video overflow-hidden rounded-lg mb-8">
              <img
                src={`/abstract-geometric-shapes.png?height=600&width=1200&query=${article.image}`}
                alt={article.title}
                className="w-full h-full object-cover"
              />
            </div>

            {/* Article Content */}
            <div className="prose prose-lg max-w-none">
              <div
                dangerouslySetInnerHTML={{ __html: article.content }}
                className="space-y-6 text-muted-foreground leading-relaxed"
              />
            </div>

            <Separator className="my-12" />

            {/* Author Info */}
            <div className="bg-secondary/30 rounded-lg p-6">
              <div className="flex items-start gap-4">
                <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center">
                  <User className="h-8 w-8 text-accent" />
                </div>
                <div className="space-y-2">
                  <h3 className="font-bold font-heading">{article.author}</h3>
                  <p className="text-sm text-muted-foreground">
                    Главный инженер компании ANAJ с 15+ летним опытом в области металлообработки и производства режущего
                    инструмента.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>

      {/* Related Articles */}
      {relatedArticles.length > 0 && (
        <section className="py-16 bg-secondary/30">
          <div className="container">
            <h2 className="text-2xl font-bold font-heading mb-8">Похожие статьи</h2>
            <div className="grid md:grid-cols-3 gap-6">
              {relatedArticles.map((relatedArticle) => (
                <Card key={relatedArticle.id} className="hover:shadow-lg transition-shadow">
                  <div className="aspect-video overflow-hidden rounded-t-lg">
                    <img
                      src={`/abstract-geometric-shapes.png?height=200&width=400&query=${relatedArticle.image}`}
                      alt={relatedArticle.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardHeader>
                    <Badge variant="outline" className="w-fit text-xs mb-2">
                      {relatedArticle.category}
                    </Badge>
                    <CardTitle className="font-heading text-lg">
                      <Link href={`/news/${relatedArticle.slug}`} className="hover:text-accent transition-colors">
                        {relatedArticle.title}
                      </Link>
                    </CardTitle>
                    <CardDescription className="line-clamp-2">{relatedArticle.excerpt}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>{relatedArticle.date}</span>
                      <span>{relatedArticle.readTime}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Footer */}
      <footer className="bg-primary text-primary-foreground py-12">
        <div className="container">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Factory className="h-6 w-6 text-accent" />
                <span className="text-xl font-bold font-heading">ANAJ.BY</span>
              </div>
              <p className="text-sm opacity-80">
                Производство и восстановление режущего инструмента высочайшего качества
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Контакты</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-accent" />
                  <span>+375 (29) 123-45-67</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-accent" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-accent" />
                  <span>г. Минск, ул. Промышленная, 15</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Услуги</h4>
              <div className="space-y-2 text-sm">
                <Link href="/services/manufacturing" className="block hover:text-accent transition-colors">
                  Производство инструмента
                </Link>
                <Link href="/services/restoration" className="block hover:text-accent transition-colors">
                  Восстановление инструмента
                </Link>
                <Link href="/services/custom" className="block hover:text-accent transition-colors">
                  Индивидуальные заказы
                </Link>
                <Link href="/services/consulting" className="block hover:text-accent transition-colors">
                  Консультации
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Информация</h4>
              <div className="space-y-2 text-sm">
                <Link href="/about" className="block hover:text-accent transition-colors">
                  О компании
                </Link>
                <Link href="/catalog" className="block hover:text-accent transition-colors">
                  Каталог продукции
                </Link>
                <Link href="/news" className="block hover:text-accent transition-colors">
                  Новости
                </Link>
                <Link href="/contacts" className="block hover:text-accent transition-colors">
                  Контакты
                </Link>
              </div>
            </div>
          </div>

          <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm opacity-80">
            <p>&copy; 2024 ANAJ.BY. Все права защищены.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
