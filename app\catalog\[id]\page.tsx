import { Button } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Phone, Mail, MapPin, Factory, ArrowLeft, ShoppingCart, Download, Share2 } from "lucide-react"
import Link from "next/link"
import { notFound } from "next/navigation"

const products = [
  {
    id: 1,
    name: "Фреза концевая HSS 8мм",
    category: "Фрезы концевые",
    material: "HSS",
    diameter: "8мм",
    price: "45.00 BYN",
    description:
      "Высокоточная концевая фреза для обработки стали и чугуна. Изготовлена из быстрорежущей стали с покрытием TiN для увеличения стойкости и производительности.",
    image: "end%20mill%208mm%20HSS%20detailed",
    inStock: true,
    specifications: {
      Диаметр: "8мм",
      Материал: "HSS",
      Покрытие: "TiN",
      "Длина общая": "75мм",
      "Длина рабочей части": "25мм",
      Хвостовик: "8мм",
      "Количество зубьев": "4",
      "Угол спирали": "30°",
      Применение: "Сталь, чугун, цветные металлы",
    },
    features: [
      "Высокая точность изготовления",
      "Покрытие TiN для увеличения стойкости",
      "Оптимальная геометрия для универсального применения",
      "Подходит для обработки различных материалов",
    ],
    applications: ["Фрезерование пазов", "Контурная обработка", "Черновая и чистовая обработка", "Обработка карманов"],
  },
]

interface ProductPageProps {
  params: {
    id: string
  }
}

export default function ProductPage({ params }: ProductPageProps) {
  const productId = Number.parseInt(params.id)
  const product = products.find((p) => p.id === productId)

  if (!product) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Factory className="h-8 w-8 text-accent" />
            <span className="text-2xl font-bold font-heading">ANAJ.BY</span>
          </Link>

          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/" className="text-sm font-medium hover:text-accent transition-colors">
              Главная
            </Link>
            <Link href="/catalog" className="text-sm font-medium text-accent">
              Каталог
            </Link>
            <Link href="/services" className="text-sm font-medium hover:text-accent transition-colors">
              Услуги
            </Link>
            <Link href="/about" className="text-sm font-medium hover:text-accent transition-colors">
              О компании
            </Link>
            <Link href="/news" className="text-sm font-medium hover:text-accent transition-colors">
              Новости
            </Link>
            <Link href="/contacts" className="text-sm font-medium hover:text-accent transition-colors">
              Контакты
            </Link>
          </nav>

          <Button className="bg-accent hover:bg-accent/90">
            <Phone className="mr-2 h-4 w-4" />
            Заказать звонок
          </Button>
        </div>
      </header>

      {/* Breadcrumbs */}
      <section className="py-4 border-b">
        <div className="container">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Link href="/" className="hover:text-accent transition-colors">
              Главная
            </Link>
            <span>/</span>
            <Link href="/catalog" className="hover:text-accent transition-colors">
              Каталог
            </Link>
            <span>/</span>
            <span className="text-foreground">{product.name}</span>
          </div>
        </div>
      </section>

      {/* Product Details */}
      <section className="py-12">
        <div className="container">
          <div className="mb-6">
            <Button variant="outline" asChild>
              <Link href="/catalog">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Назад к каталогу
              </Link>
            </Button>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Product Image */}
            <div className="space-y-4">
              <div className="aspect-square overflow-hidden rounded-lg border">
                <img
                  src={`/abstract-geometric-shapes.png?height=600&width=600&query=${product.image}`}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="grid grid-cols-4 gap-2">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className="aspect-square overflow-hidden rounded border cursor-pointer hover:border-accent transition-colors"
                  >
                    <img
                      src={`/abstract-geometric-shapes.png?height=150&width=150&query=${product.image}%20angle%20${i}`}
                      alt={`${product.name} вид ${i}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              <div>
                <Badge variant="outline" className="mb-2">
                  {product.category}
                </Badge>
                <h1 className="text-3xl font-bold font-heading mb-2">{product.name}</h1>
                <p className="text-muted-foreground leading-relaxed">{product.description}</p>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-accent">{product.price}</div>
                <Badge
                  variant={product.inStock ? "secondary" : "destructive"}
                  className={product.inStock ? "bg-green-100 text-green-800" : ""}
                >
                  {product.inStock ? "В наличии" : "Нет в наличии"}
                </Badge>
              </div>

              <div className="flex gap-3">
                <Button size="lg" className="flex-1 bg-accent hover:bg-accent/90" disabled={!product.inStock}>
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  Оставить заявку
                </Button>
                <Button variant="outline" size="lg">
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>

              <Separator />

              {/* Quick Specs */}
              <div className="space-y-3">
                <h3 className="font-bold font-heading">Основные характеристики</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Материал:</span>
                    <span className="font-medium">{product.material}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Диаметр:</span>
                    <span className="font-medium">{product.diameter}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Покрытие:</span>
                    <span className="font-medium">{product.specifications["Покрытие"]}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Длина:</span>
                    <span className="font-medium">{product.specifications["Длина общая"]}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Detailed Information */}
          <div className="mt-16 grid lg:grid-cols-3 gap-8">
            {/* Full Specifications */}
            <Card>
              <CardHeader>
                <CardTitle className="font-heading">Технические характеристики</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {Object.entries(product.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-muted-foreground">{key}:</span>
                    <span className="font-medium">{value}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Features */}
            <Card>
              <CardHeader>
                <CardTitle className="font-heading">Особенности</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-accent mr-2">•</span>
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Applications */}
            <Card>
              <CardHeader>
                <CardTitle className="font-heading">Применение</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {product.applications.map((application, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-accent mr-2">•</span>
                      <span className="text-sm">{application}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Download Section */}
          <div className="mt-12 text-center">
            <Card className="inline-block">
              <CardContent className="p-6">
                <h3 className="font-bold font-heading mb-4">Техническая документация</h3>
                <div className="flex gap-4 justify-center">
                  <Button variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Скачать чертеж
                  </Button>
                  <Button variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Техпаспорт PDF
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-primary text-primary-foreground py-12">
        <div className="container">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Factory className="h-6 w-6 text-accent" />
                <span className="text-xl font-bold font-heading">ANAJ.BY</span>
              </div>
              <p className="text-sm opacity-80">
                Производство и восстановление режущего инструмента высочайшего качества
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Контакты</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-accent" />
                  <span>+375 (29) 123-45-67</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-accent" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-accent" />
                  <span>г. Минск, ул. Промышленная, 15</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Услуги</h4>
              <div className="space-y-2 text-sm">
                <Link href="#" className="block hover:text-accent transition-colors">
                  Производство инструмента
                </Link>
                <Link href="#" className="block hover:text-accent transition-colors">
                  Восстановление инструмента
                </Link>
                <Link href="#" className="block hover:text-accent transition-colors">
                  Индивидуальные заказы
                </Link>
                <Link href="#" className="block hover:text-accent transition-colors">
                  Консультации
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Информация</h4>
              <div className="space-y-2 text-sm">
                <Link href="/about" className="block hover:text-accent transition-colors">
                  О компании
                </Link>
                <Link href="/catalog" className="block hover:text-accent transition-colors">
                  Каталог продукции
                </Link>
                <Link href="/news" className="block hover:text-accent transition-colors">
                  Новости
                </Link>
                <Link href="/contacts" className="block hover:text-accent transition-colors">
                  Контакты
                </Link>
              </div>
            </div>
          </div>

          <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm opacity-80">
            <p>&copy; 2024 ANAJ.BY. Все права защищены.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
