import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Phone, Mail, MapPin, Factory, Shield, Target, Eye, Heart, Zap } from "lucide-react"
import Link from "next/link"

const milestones = [
  {
    year: "2009",
    title: "Основание компании",
    description: "Создание ANAJ с фокусом на производство качественного режущего инструмента",
  },
  {
    year: "2012",
    title: "Расширение производства",
    description: "Приобретение современного оборудования и увеличение производственных мощностей",
  },
  {
    year: "2015",
    title: "Запуск услуг восстановления",
    description: "Внедрение технологий восстановления и переточки режущего инструмента",
  },
  {
    year: "2018",
    title: "Международное признание",
    description: "Получение сертификатов качества и выход на международные рынки",
  },
  {
    year: "2021",
    title: "Цифровизация процессов",
    description: "Внедрение современных систем управления производством и качеством",
  },
  {
    year: "2024",
    title: "Новые технологии",
    description: "Запуск производства инструмента с инновационными покрытиями",
  },
]

const team = [
  {
    name: "Александр Новиков",
    position: "Генеральный директор",
    experience: "20+ лет в машиностроении",
    description: "Эксперт в области металлообработки и управления производством",
    image: "ceo%20alexander%20novikov%20industrial",
  },
  {
    name: "Елена Петрова",
    position: "Главный инженер",
    experience: "15+ лет в инструментальном производстве",
    description: "Специалист по разработке и внедрению новых технологий",
    image: "chief%20engineer%20elena%20petrova",
  },
  {
    name: "Михаил Сидоров",
    position: "Начальник производства",
    experience: "18+ лет в производстве инструмента",
    description: "Отвечает за качество и соблюдение технологических процессов",
    image: "production%20manager%20mikhail%20sidorov",
  },
  {
    name: "Ольга Козлова",
    position: "Руководитель отдела качества",
    experience: "12+ лет в контроле качества",
    description: "Обеспечивает соответствие продукции высочайшим стандартам",
    image: "quality%20manager%20olga%20kozlova",
  },
]

const partners = [
  {
    name: "Минский тракторный завод",
    description: "Поставка специального инструмента для производства тракторов",
    logo: "mtz%20logo%20partner",
  },
  {
    name: "БелАЗ",
    description: "Производство и восстановление инструмента для горнодобывающей техники",
    logo: "belaz%20logo%20partner",
  },
  {
    name: "Гомсельмаш",
    description: "Комплексные решения для производства сельхозтехники",
    logo: "gomselmash%20logo%20partner",
  },
  {
    name: "Минский автомобильный завод",
    description: "Специализированный инструмент для автомобильной промышленности",
    logo: "maz%20logo%20partner",
  },
]

const values = [
  {
    icon: Target,
    title: "Точность",
    description: "Высочайшая точность в каждом изделии и процессе",
  },
  {
    icon: Shield,
    title: "Качество",
    description: "Непрерывный контроль качества на всех этапах производства",
  },
  {
    icon: Zap,
    title: "Инновации",
    description: "Постоянное внедрение новых технологий и решений",
  },
  {
    icon: Heart,
    title: "Надежность",
    description: "Долгосрочные партнерские отношения с клиентами",
  },
]

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Factory className="h-8 w-8 text-accent" />
            <span className="text-2xl font-bold font-heading">ANAJ.BY</span>
          </Link>

          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/" className="text-sm font-medium hover:text-accent transition-colors">
              Главная
            </Link>
            <Link href="/catalog" className="text-sm font-medium hover:text-accent transition-colors">
              Каталог
            </Link>
            <Link href="/services" className="text-sm font-medium hover:text-accent transition-colors">
              Услуги
            </Link>
            <Link href="/about" className="text-sm font-medium text-accent">
              О компании
            </Link>
            <Link href="/news" className="text-sm font-medium hover:text-accent transition-colors">
              Новости
            </Link>
            <Link href="/contacts" className="text-sm font-medium hover:text-accent transition-colors">
              Контакты
            </Link>
          </nav>

          <Button className="bg-accent hover:bg-accent/90">
            <Phone className="mr-2 h-4 w-4" />
            Заказать звонок
          </Button>
        </div>
      </header>

      {/* Page Header */}
      <section className="py-12 bg-secondary/30">
        <div className="container">
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold font-heading">О компании ANAJ</h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              15+ лет опыта в производстве и восстановлении режущего инструмента высочайшего качества
            </p>
          </div>
        </div>
      </section>

      {/* Company Story */}
      <section className="py-16">
        <div className="container">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold font-heading">Наша история</h2>
              <p className="text-muted-foreground leading-relaxed">
                Компания ANAJ была основана в 2009 году группой опытных инженеров и технологов, объединенных общей целью
                - создание высококачественного режущего инструмента для промышленных предприятий Беларуси и стран СНГ.
              </p>
              <p className="text-muted-foreground leading-relaxed">
                За годы работы мы прошли путь от небольшой мастерской до современного производственного предприятия,
                оснащенного высокоточным оборудованием и укомплектованного командой профессионалов высочайшего уровня.
              </p>
              <p className="text-muted-foreground leading-relaxed">
                Сегодня ANAJ - это признанный лидер в области производства и восстановления режущего инструмента,
                партнер ведущих промышленных предприятий и надежный поставщик качественных решений для металлообработки.
              </p>
            </div>

            <div className="relative">
              <img
                src="/abstract-geometric-shapes.png?height=500&width=600&query=anaj%20company%20history%20factory%20building"
                alt="История компании ANAJ"
                className="rounded-lg shadow-lg w-full"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Mission and Vision */}
      <section className="py-16 bg-secondary/30">
        <div className="container">
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="text-center">
              <CardHeader>
                <Eye className="h-12 w-12 text-accent mx-auto mb-4" />
                <CardTitle className="font-heading text-2xl">Наша миссия</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  Обеспечивать промышленные предприятия высококачественным режущим инструментом, способствуя повышению
                  эффективности производства и конкурентоспособности белорусской промышленности на международном рынке.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Target className="h-12 w-12 text-accent mx-auto mb-4" />
                <CardTitle className="font-heading text-2xl">Наше видение</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  Стать ведущим производителем режущего инструмента в регионе, признанным за инновационные решения,
                  безупречное качество продукции и высокий уровень сервиса для наших клиентов.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Company Values */}
      <section className="py-16">
        <div className="container">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold font-heading">Наши ценности</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Принципы, которыми мы руководствуемся в работе и которые определяют качество наших услуг
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon
              return (
                <div key={index} className="text-center space-y-4">
                  <IconComponent className="h-16 w-16 text-accent mx-auto" />
                  <h3 className="text-xl font-bold font-heading">{value.title}</h3>
                  <p className="text-muted-foreground text-sm">{value.description}</p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-16 bg-secondary/30">
        <div className="container">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold font-heading">Этапы развития</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">Ключевые моменты в истории развития компании ANAJ</p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-accent/20 hidden lg:block"></div>

            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <div
                  key={index}
                  className={`flex items-center gap-8 ${index % 2 === 0 ? "lg:flex-row" : "lg:flex-row-reverse"}`}
                >
                  <div className={`flex-1 ${index % 2 === 0 ? "lg:text-right" : "lg:text-left"}`}>
                    <Card className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div
                          className={`flex items-center gap-3 ${index % 2 === 0 ? "lg:flex-row-reverse lg:justify-end" : "lg:flex-row lg:justify-start"}`}
                        >
                          <Badge variant="outline" className="text-accent border-accent">
                            {milestone.year}
                          </Badge>
                          <CardTitle className="font-heading">{milestone.title}</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground">{milestone.description}</p>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="hidden lg:flex w-4 h-4 bg-accent rounded-full border-4 border-background shadow-lg relative z-10"></div>

                  <div className="flex-1 hidden lg:block"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-16">
        <div className="container">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold font-heading">Наша команда</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Опытные профессионалы, которые обеспечивают высокое качество продукции и услуг
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="w-24 h-24 mx-auto mb-4 rounded-full overflow-hidden bg-secondary">
                    <img
                      src={`/abstract-geometric-shapes.png?height=200&width=200&query=${member.image}`}
                      alt={member.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardTitle className="font-heading">{member.name}</CardTitle>
                  <CardDescription className="text-accent font-medium">{member.position}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Badge variant="secondary" className="text-xs">
                    {member.experience}
                  </Badge>
                  <p className="text-sm text-muted-foreground">{member.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Statistics */}
      <section className="py-16 bg-secondary/30">
        <div className="container">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div className="space-y-2">
              <div className="text-4xl font-bold text-accent">15+</div>
              <div className="text-sm text-muted-foreground">Лет опыта</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl font-bold text-accent">500+</div>
              <div className="text-sm text-muted-foreground">Довольных клиентов</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl font-bold text-accent">10000+</div>
              <div className="text-sm text-muted-foreground">Изготовленных инструментов</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl font-bold text-accent">99%</div>
              <div className="text-sm text-muted-foreground">Качество продукции</div>
            </div>
          </div>
        </div>
      </section>

      {/* Partners */}
      <section className="py-16">
        <div className="container">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold font-heading">Наши партнеры</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Ведущие промышленные предприятия, которые доверяют нам свои проекты
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {partners.map((partner, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-secondary rounded-lg flex items-center justify-center flex-shrink-0">
                      <img
                        src={`/abstract-geometric-shapes.png?height=64&width=64&query=${partner.logo}`}
                        alt={partner.name}
                        className="w-12 h-12 object-contain"
                      />
                    </div>
                    <div className="space-y-2">
                      <h3 className="font-bold font-heading">{partner.name}</h3>
                      <p className="text-sm text-muted-foreground">{partner.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-secondary/30">
        <div className="container text-center space-y-6">
          <h2 className="text-3xl font-bold font-heading">Готовы стать нашим партнером?</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Свяжитесь с нами, чтобы обсудить возможности сотрудничества и получить персональное предложение
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-accent hover:bg-accent/90" asChild>
              <Link href="/contacts">Связаться с нами</Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/catalog">Посмотреть каталог</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-primary text-primary-foreground py-12">
        <div className="container">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Factory className="h-6 w-6 text-accent" />
                <span className="text-xl font-bold font-heading">ANAJ.BY</span>
              </div>
              <p className="text-sm opacity-80">
                Производство и восстановление режущего инструмента высочайшего качества
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Контакты</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-accent" />
                  <span>+375 (29) 123-45-67</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-accent" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-accent" />
                  <span>г. Минск, ул. Промышленная, 15</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Услуги</h4>
              <div className="space-y-2 text-sm">
                <Link href="/services/manufacturing" className="block hover:text-accent transition-colors">
                  Производство инструмента
                </Link>
                <Link href="/services/restoration" className="block hover:text-accent transition-colors">
                  Восстановление инструмента
                </Link>
                <Link href="/services/custom" className="block hover:text-accent transition-colors">
                  Индивидуальные заказы
                </Link>
                <Link href="/services/consulting" className="block hover:text-accent transition-colors">
                  Консультации
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Информация</h4>
              <div className="space-y-2 text-sm">
                <Link href="/about" className="block hover:text-accent transition-colors">
                  О компании
                </Link>
                <Link href="/catalog" className="block hover:text-accent transition-colors">
                  Каталог продукции
                </Link>
                <Link href="/news" className="block hover:text-accent transition-colors">
                  Новости
                </Link>
                <Link href="/contacts" className="block hover:text-accent transition-colors">
                  Контакты
                </Link>
              </div>
            </div>
          </div>

          <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm opacity-80">
            <p>&copy; 2024 ANAJ.BY. Все права защищены.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
