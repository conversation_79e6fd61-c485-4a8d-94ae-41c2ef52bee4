import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Phone, Mail, MapPin, Factory, Search, Filter, Download, ShoppingCart } from "lucide-react"
import Link from "next/link"

const products = [
  {
    id: 1,
    name: "Фреза концевая HSS 8мм",
    category: "Фрезы концевые",
    material: "HSS",
    diameter: "8мм",
    price: "45.00 BYN",
    description: "Высокоточная концевая фреза для обработки стали и чугуна",
    image: "end%20mill%208mm%20HSS",
    inStock: true,
    specifications: {
      Диаметр: "8мм",
      Материал: "HSS",
      Покрытие: "TiN",
      Длина: "75мм",
      Хвостовик: "8мм",
    },
  },
  {
    id: 2,
    name: "Сверло спиральное 12мм",
    category: "Сверла спиральные",
    material: "HSS-Co",
    diameter: "12мм",
    price: "28.50 BYN",
    description: "Спиральное сверло с кобальтовым покрытием для твердых материалов",
    image: "spiral%20drill%2012mm%20cobalt",
    inStock: true,
    specifications: {
      Диаметр: "12мм",
      Материал: "HSS-Co",
      Покрытие: "TiAlN",
      Длина: "151мм",
      Хвостовик: "12мм",
    },
  },
  {
    id: 3,
    name: "Резец токарный TNMG",
    category: "Резцы токарные",
    material: "Твердый сплав",
    diameter: "16мм",
    price: "85.00 BYN",
    description: "Токарный резец с твердосплавной пластиной для точения",
    image: "turning%20tool%20TNMG%20carbide",
    inStock: false,
    specifications: {
      Тип: "TNMG 160408",
      Материал: "Твердый сплав",
      Покрытие: "CVD",
      Державка: "16x16мм",
      Применение: "Сталь, чугун",
    },
  },
  {
    id: 4,
    name: "Развертка машинная 10H7",
    category: "Развертки",
    material: "HSS",
    diameter: "10мм",
    price: "52.00 BYN",
    description: "Машинная развертка для точной обработки отверстий",
    image: "machine%20reamer%2010mm%20H7",
    inStock: true,
    specifications: {
      Диаметр: "10мм",
      Точность: "H7",
      Материал: "HSS",
      Длина: "100мм",
      Хвостовик: "10мм",
    },
  },
  {
    id: 5,
    name: "Метчик М12x1.75",
    category: "Метчики",
    material: "HSS",
    diameter: "М12",
    price: "35.00 BYN",
    description: "Метчик для нарезания внутренней резьбы М12x1.75",
    image: "tap%20M12%20thread%20cutting",
    inStock: true,
    specifications: {
      Резьба: "М12x1.75",
      Материал: "HSS",
      Покрытие: "TiN",
      Длина: "90мм",
      Квадрат: "9мм",
    },
  },
  {
    id: 6,
    name: "Плашка М16x2.0",
    category: "Плашки",
    material: "HSS",
    diameter: "М16",
    price: "42.00 BYN",
    description: "Плашка для нарезания наружной резьбы М16x2.0",
    image: "die%20M16%20external%20threading",
    inStock: true,
    specifications: {
      Резьба: "М16x2.0",
      Материал: "HSS",
      Диаметр: "45мм",
      Толщина: "14мм",
      "Класс точности": "6H",
    },
  },
]

const categories = [
  "Все категории",
  "Фрезы концевые",
  "Сверла спиральные",
  "Резцы токарные",
  "Развертки",
  "Метчики",
  "Плашки",
]

const materials = ["Все материалы", "HSS", "HSS-Co", "Твердый сплав", "Быстрорежущая сталь"]

export default function CatalogPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Factory className="h-8 w-8 text-accent" />
            <span className="text-2xl font-bold font-heading">ANAJ.BY</span>
          </Link>

          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/" className="text-sm font-medium hover:text-accent transition-colors">
              Главная
            </Link>
            <Link href="/catalog" className="text-sm font-medium text-accent">
              Каталог
            </Link>
            <Link href="/services" className="text-sm font-medium hover:text-accent transition-colors">
              Услуги
            </Link>
            <Link href="/about" className="text-sm font-medium hover:text-accent transition-colors">
              О компании
            </Link>
            <Link href="/news" className="text-sm font-medium hover:text-accent transition-colors">
              Новости
            </Link>
            <Link href="/contacts" className="text-sm font-medium hover:text-accent transition-colors">
              Контакты
            </Link>
          </nav>

          <Button className="bg-accent hover:bg-accent/90">
            <Phone className="mr-2 h-4 w-4" />
            Заказать звонок
          </Button>
        </div>
      </header>

      {/* Page Header */}
      <section className="py-12 bg-secondary/30">
        <div className="container">
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold font-heading">Каталог продукции</h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Полный ассортимент режущего инструмента высочайшего качества для промышленных предприятий
            </p>
          </div>
        </div>
      </section>

      {/* Filters and Search */}
      <section className="py-8 border-b">
        <div className="container">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 w-full lg:w-auto">
              <div className="relative flex-1 lg:w-80">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input placeholder="Поиск по каталогу..." className="pl-10" />
              </div>

              <Select defaultValue="Все категории">
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select defaultValue="Все материалы">
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {materials.map((material) => (
                    <SelectItem key={material} value={material}>
                      {material}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Фильтры
              </Button>
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                PDF каталог
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-12">
        <div className="container">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <Card key={product.id} className="hover:shadow-lg transition-shadow group">
                <div className="aspect-square overflow-hidden rounded-t-lg relative">
                  <img
                    src={`/abstract-geometric-shapes.png?height=300&width=300&query=${product.image}`}
                    alt={product.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  {!product.inStock && (
                    <Badge variant="destructive" className="absolute top-2 right-2">
                      Нет в наличии
                    </Badge>
                  )}
                  {product.inStock && (
                    <Badge variant="secondary" className="absolute top-2 right-2 bg-green-100 text-green-800">
                      В наличии
                    </Badge>
                  )}
                </div>

                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <Badge variant="outline" className="text-xs">
                      {product.category}
                    </Badge>
                    <span className="text-lg font-bold text-accent">{product.price}</span>
                  </div>
                  <CardTitle className="font-heading text-lg leading-tight">{product.name}</CardTitle>
                  <CardDescription className="text-sm">{product.description}</CardDescription>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Материал:</span>
                      <span className="font-medium">{product.material}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Диаметр:</span>
                      <span className="font-medium">{product.diameter}</span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1 bg-transparent" asChild>
                      <Link href={`/catalog/${product.id}`}>Подробнее</Link>
                    </Button>
                    <Button size="sm" className="bg-accent hover:bg-accent/90" disabled={!product.inStock}>
                      <ShoppingCart className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Показать еще товары
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-secondary/30">
        <div className="container text-center space-y-6">
          <h2 className="text-3xl font-bold font-heading">Не нашли нужный инструмент?</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Мы изготавливаем инструмент по индивидуальным техническим требованиям. Свяжитесь с нашими специалистами для
            консультации.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-accent hover:bg-accent/90">
              Индивидуальный заказ
            </Button>
            <Button size="lg" variant="outline">
              <Phone className="mr-2 h-4 w-4" />
              Консультация инженера
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-primary text-primary-foreground py-12">
        <div className="container">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Factory className="h-6 w-6 text-accent" />
                <span className="text-xl font-bold font-heading">ANAJ.BY</span>
              </div>
              <p className="text-sm opacity-80">
                Производство и восстановление режущего инструмента высочайшего качества
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Контакты</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-accent" />
                  <span>+375 (29) 123-45-67</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-accent" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-accent" />
                  <span>г. Минск, ул. Промышленная, 15</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Услуги</h4>
              <div className="space-y-2 text-sm">
                <Link href="#" className="block hover:text-accent transition-colors">
                  Производство инструмента
                </Link>
                <Link href="#" className="block hover:text-accent transition-colors">
                  Восстановление инструмента
                </Link>
                <Link href="#" className="block hover:text-accent transition-colors">
                  Индивидуальные заказы
                </Link>
                <Link href="#" className="block hover:text-accent transition-colors">
                  Консультации
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Информация</h4>
              <div className="space-y-2 text-sm">
                <Link href="/about" className="block hover:text-accent transition-colors">
                  О компании
                </Link>
                <Link href="/catalog" className="block hover:text-accent transition-colors">
                  Каталог продукции
                </Link>
                <Link href="/news" className="block hover:text-accent transition-colors">
                  Новости
                </Link>
                <Link href="/contacts" className="block hover:text-accent transition-colors">
                  Контакты
                </Link>
              </div>
            </div>
          </div>

          <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm opacity-80">
            <p>&copy; 2024 ANAJ.BY. Все права защищены.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
