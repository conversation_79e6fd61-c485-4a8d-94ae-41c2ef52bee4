import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Phone, Mail, MapPin, Factory, ArrowLeft, CheckCircle, Award, Shield, Users } from "lucide-react"
import Link from "next/link"
import { notFound } from "next/navigation"

const services = [
  {
    id: "manufacturing",
    title: "Производство инструмента",
    icon: Factory,
    description:
      "Изготовление режущего инструмента по индивидуальным техническим требованиям с использованием современного высокоточного оборудования",
    fullDescription:
      "Наша компания специализируется на производстве высококачественного режущего инструмента для различных отраслей промышленности. Мы используем современное оборудование и передовые технологии для создания инструментов, которые отвечают самым строгим требованиям качества и производительности.",
    features: [
      "Фрезы концевые всех типов и размеров",
      "Сверла спиральные с различными покрытиями",
      "Резцы токарные с твердосплавными пластинами",
      "Развертки машинные и ручные",
      "Метчики и плашки для нарезания резьбы",
      "Специальный инструмент по чертежам заказчика",
    ],
    process: [
      "Анализ технических требований и чертежей",
      "Разработка оптимальной конструкции инструмента",
      "Изготовление на высокоточном оборудовании",
      "Многоступенчатый контроль качества",
      "Упаковка и отгрузка готовой продукции",
    ],
    advantages: [
      "Опыт производства более 15 лет",
      "Современное высокоточное оборудование",
      "Контроль качества на каждом этапе",
      "Полная гарантия на всю продукцию",
      "Индивидуальный подход к каждому заказу",
      "Соблюдение сроков поставки",
    ],
    applications: [
      "Машиностроение и металлообработка",
      "Автомобильная промышленность",
      "Авиационная и космическая отрасль",
      "Энергетическое машиностроение",
      "Производство медицинского оборудования",
      "Точное приборостроение",
    ],
    image: "manufacturing%20cutting%20tools%20factory%20detailed",
  },
  {
    id: "restoration",
    title: "Восстановление инструмента",
    icon: Users,
    description:
      "Профессиональное восстановление изношенного режущего инструмента с восстановлением первоначальных характеристик и геометрии",
    fullDescription:
      "Восстановление режущего инструмента - это экономически выгодная альтернатива покупке нового инструмента. Наши специалисты используют современные технологии переточки и восстановления для возвращения инструменту первоначальных рабочих характеристик.",
    features: [
      "Восстановление концевых фрез всех типов",
      "Переточка сверл и зенкеров",
      "Восстановление резцов и пластин",
      "Ремонт разверток и метчиков",
      "Восстановление специального инструмента",
      "Нанесение защитных покрытий",
    ],
    process: [
      "Диагностика и оценка состояния инструмента",
      "Определение оптимальной технологии восстановления",
      "Переточка и восстановление геометрии",
      "Нанесение покрытий при необходимости",
      "Контроль качества и финальные испытания",
    ],
    advantages: [
      "Экономия до 70% от стоимости нового инструмента",
      "Восстановление первоначальных характеристик",
      "Быстрые сроки выполнения работ",
      "Гарантия качества на все виды работ",
      "Возможность улучшения характеристик",
      "Экологичность процесса",
    ],
    applications: [
      "Восстановление дорогостоящего инструмента",
      "Ремонт специального и нестандартного инструмента",
      "Продление срока службы инструмента",
      "Восстановление геометрии после износа",
      "Нанесение новых покрытий",
      "Модернизация старого инструмента",
    ],
    image: "tool%20restoration%20sharpening%20service%20detailed",
  },
  {
    id: "custom",
    title: "Индивидуальные заказы",
    icon: Award,
    description:
      "Разработка и изготовление специального режущего инструмента под конкретные технологические задачи заказчика",
    fullDescription:
      "Мы разрабатываем и изготавливаем специальный режущий инструмент для решения уникальных технологических задач. Наши инженеры работают в тесном сотрудничестве с заказчиками для создания оптимальных решений.",
    features: [
      "Проектирование по техническому заданию",
      "Изготовление опытных образцов",
      "Специальные геометрии и покрытия",
      "Нестандартные размеры и формы",
      "Комплексные технологические решения",
      "Техническое сопровождение внедрения",
    ],
    process: [
      "Анализ технологической задачи заказчика",
      "Разработка оптимального технического решения",
      "Согласование конструкции с заказчиком",
      "Изготовление и всесторонние испытания",
      "Внедрение и постоянная техническая поддержка",
    ],
    advantages: [
      "Индивидуальный подход к каждой задаче",
      "Оптимизация технологических процессов",
      "Значительное повышение производительности",
      "Полное техническое сопровождение проекта",
      "Гарантия достижения заявленных параметров",
      "Возможность серийного производства",
    ],
    applications: [
      "Обработка сложных профилей и поверхностей",
      "Работа с труднообрабатываемыми материалами",
      "Высокоскоростная обработка",
      "Прецизионная обработка",
      "Комбинированные операции",
      "Автоматизированное производство",
    ],
    image: "custom%20cutting%20tools%20design%20detailed",
  },
  {
    id: "consulting",
    title: "Консультация инженера",
    icon: Shield,
    description:
      "Техническая поддержка и консультации по выбору оптимального режущего инструмента и технологических решений",
    fullDescription:
      "Наши опытные инженеры предоставляют комплексные консультационные услуги по оптимизации процессов металлообработки. Мы помогаем выбрать правильный инструмент и настроить технологические параметры для достижения максимальной эффективности.",
    features: [
      "Подбор оптимального инструмента для задач",
      "Расчет и оптимизация режимов резания",
      "Анализ и улучшение технологических процессов",
      "Рекомендации по повышению эффективности",
      "Обучение и подготовка персонала заказчика",
      "Круглосуточная техническая поддержка",
    ],
    process: [
      "Детальный анализ производственных задач",
      "Изучение существующих технологических процессов",
      "Разработка персонализированных рекомендаций",
      "Презентация решений руководству заказчика",
      "Сопровождение процесса внедрения изменений",
    ],
    advantages: [
      "Глубокие экспертные знания и многолетний опыт",
      "Комплексный системный подход к решению задач",
      "Значительное повышение эффективности производства",
      "Существенное снижение производственных затрат",
      "Оптимизация использования оборудования",
      "Повышение качества выпускаемой продукции",
    ],
    applications: [
      "Аудит существующих технологических процессов",
      "Внедрение новых технологий обработки",
      "Решение проблем качества обработки",
      "Оптимизация инструментального парка",
      "Обучение технического персонала",
      "Техническое сопровождение проектов",
    ],
    image: "engineering%20consultation%20technical%20support%20detailed",
  },
]

interface ServicePageProps {
  params: {
    id: string
  }
}

export default function ServicePage({ params }: ServicePageProps) {
  const service = services.find((s) => s.id === params.id)

  if (!service) {
    notFound()
  }

  const IconComponent = service.icon

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Factory className="h-8 w-8 text-accent" />
            <span className="text-2xl font-bold font-heading">ANAJ.BY</span>
          </Link>

          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/" className="text-sm font-medium hover:text-accent transition-colors">
              Главная
            </Link>
            <Link href="/catalog" className="text-sm font-medium hover:text-accent transition-colors">
              Каталог
            </Link>
            <Link href="/services" className="text-sm font-medium text-accent">
              Услуги
            </Link>
            <Link href="/about" className="text-sm font-medium hover:text-accent transition-colors">
              О компании
            </Link>
            <Link href="/news" className="text-sm font-medium hover:text-accent transition-colors">
              Новости
            </Link>
            <Link href="/contacts" className="text-sm font-medium hover:text-accent transition-colors">
              Контакты
            </Link>
          </nav>

          <Button className="bg-accent hover:bg-accent/90">
            <Phone className="mr-2 h-4 w-4" />
            Заказать звонок
          </Button>
        </div>
      </header>

      {/* Breadcrumbs */}
      <section className="py-4 border-b">
        <div className="container">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Link href="/" className="hover:text-accent transition-colors">
              Главная
            </Link>
            <span>/</span>
            <Link href="/services" className="hover:text-accent transition-colors">
              Услуги
            </Link>
            <span>/</span>
            <span className="text-foreground">{service.title}</span>
          </div>
        </div>
      </section>

      {/* Service Header */}
      <section className="py-12 bg-secondary/30">
        <div className="container">
          <div className="mb-6">
            <Button variant="outline" asChild>
              <Link href="/services">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Назад к услугам
              </Link>
            </Button>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <IconComponent className="h-12 w-12 text-accent" />
                <h1 className="text-4xl font-bold font-heading">{service.title}</h1>
              </div>
              <p className="text-xl text-muted-foreground leading-relaxed">{service.description}</p>
              <p className="text-muted-foreground leading-relaxed">{service.fullDescription}</p>
            </div>

            <div className="relative">
              <img
                src={`/abstract-geometric-shapes.png?height=400&width=600&query=${service.image}`}
                alt={service.title}
                className="rounded-lg shadow-lg w-full"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Service Details */}
      <section className="py-16">
        <div className="container">
          <div className="grid lg:grid-cols-2 gap-16">
            {/* Features and Process */}
            <div className="space-y-12">
              {/* What We Offer */}
              <Card>
                <CardHeader>
                  <CardTitle className="font-heading">Что мы предлагаем</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {service.features.map((feature, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-accent mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Process */}
              <Card>
                <CardHeader>
                  <CardTitle className="font-heading">Процесс работы</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {service.process.map((step, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <Badge variant="outline" className="min-w-[2rem] h-8 flex items-center justify-center">
                        {index + 1}
                      </Badge>
                      <span className="pt-1">{step}</span>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Advantages and Applications */}
            <div className="space-y-12">
              {/* Advantages */}
              <Card>
                <CardHeader>
                  <CardTitle className="font-heading">Наши преимущества</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {service.advantages.map((advantage, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <Award className="h-5 w-5 text-accent mt-0.5 flex-shrink-0" />
                      <span>{advantage}</span>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Applications */}
              <Card>
                <CardHeader>
                  <CardTitle className="font-heading">Области применения</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {service.applications.map((application, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <Factory className="h-5 w-5 text-accent mt-0.5 flex-shrink-0" />
                      <span>{application}</span>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Request Form */}
      <section className="py-16 bg-secondary/30">
        <div className="container">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="font-heading text-2xl">Оставить заявку на услугу</CardTitle>
                <CardDescription>
                  Заполните форму, и наш специалист свяжется с вами для обсуждения деталей
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Имя *</label>
                    <Input placeholder="Ваше имя" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Телефон *</label>
                    <Input placeholder="+375 (29) 123-45-67" />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Email</label>
                  <Input placeholder="<EMAIL>" type="email" />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Компания</label>
                  <Input placeholder="Название компании" />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Описание задачи *</label>
                  <Textarea placeholder="Опишите ваши требования и задачи..." className="min-h-[120px]" />
                </div>

                <Button className="w-full bg-accent hover:bg-accent/90" size="lg">
                  Отправить заявку
                </Button>

                <p className="text-xs text-muted-foreground text-center">
                  Нажимая кнопку, вы соглашаетесь с обработкой персональных данных
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-primary text-primary-foreground py-12">
        <div className="container">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Factory className="h-6 w-6 text-accent" />
                <span className="text-xl font-bold font-heading">ANAJ.BY</span>
              </div>
              <p className="text-sm opacity-80">
                Производство и восстановление режущего инструмента высочайшего качества
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Контакты</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-accent" />
                  <span>+375 (29) 123-45-67</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-accent" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-accent" />
                  <span>г. Минск, ул. Промышленная, 15</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Услуги</h4>
              <div className="space-y-2 text-sm">
                <Link href="/services/manufacturing" className="block hover:text-accent transition-colors">
                  Производство инструмента
                </Link>
                <Link href="/services/restoration" className="block hover:text-accent transition-colors">
                  Восстановление инструмента
                </Link>
                <Link href="/services/custom" className="block hover:text-accent transition-colors">
                  Индивидуальные заказы
                </Link>
                <Link href="/services/consulting" className="block hover:text-accent transition-colors">
                  Консультации
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Информация</h4>
              <div className="space-y-2 text-sm">
                <Link href="/about" className="block hover:text-accent transition-colors">
                  О компании
                </Link>
                <Link href="/catalog" className="block hover:text-accent transition-colors">
                  Каталог продукции
                </Link>
                <Link href="/news" className="block hover:text-accent transition-colors">
                  Новости
                </Link>
                <Link href="/contacts" className="block hover:text-accent transition-colors">
                  Контакты
                </Link>
              </div>
            </div>
          </div>

          <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm opacity-80">
            <p>&copy; 2024 ANAJ.BY. Все права защищены.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
