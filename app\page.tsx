import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Phone, Mail, MapPin, Factory, Wrench, Settings, Users, Award, Clock, Shield } from "lucide-react"
import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-2">
            <Factory className="h-8 w-8 text-accent" />
            <span className="text-2xl font-bold font-heading">ANAJ.BY</span>
          </div>

          <nav className="hidden md:flex items-center space-x-6">
            <a href="#home" className="text-sm font-medium hover:text-accent transition-colors">
              Главная
            </a>
            <Link href="/catalog" className="text-sm font-medium hover:text-accent transition-colors">
              Каталог
            </Link>
            <Link href="/services" className="text-sm font-medium hover:text-accent transition-colors">
              Услуги
            </Link>
            <Link href="/about" className="text-sm font-medium hover:text-accent transition-colors">
              О компании
            </Link>
            <Link href="/news" className="text-sm font-medium hover:text-accent transition-colors">
              Новости
            </Link>
            <a href="#contacts" className="text-sm font-medium hover:text-accent transition-colors">
              Контакты
            </a>
          </nav>

          <Button className="bg-accent hover:bg-accent/90">
            <Phone className="mr-2 h-4 w-4" />
            Заказать звонок
          </Button>
        </div>
      </header>

      {/* Hero Section */}
      <section
        id="home"
        className="relative min-h-[80vh] flex items-center justify-center bg-gradient-to-br from-secondary/50 to-background"
      >
        <div className="absolute inset-0 bg-[url('/placeholder-kf64o.png')] bg-cover bg-center opacity-10"></div>
        <div className="relative z-10 container text-center space-y-8">
          <h1 className="text-4xl md:text-6xl font-bold font-heading text-balance">
            Производство и восстановление
            <span className="text-accent block">режущего инструмента</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto text-pretty">
            Высокоточное оборудование, опыт 15+ лет, гарантия качества для промышленных предприятий и мастеров
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-accent hover:bg-accent/90" asChild>
              <Link href="/catalog">Каталог продукции</Link>
            </Button>
            <Button size="lg" variant="outline">
              <Wrench className="mr-2 h-4 w-4" />
              Восстановить инструмент
            </Button>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 bg-secondary/30">
        <div className="container">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold font-heading">О компании ANAJ</h2>
              <p className="text-muted-foreground leading-relaxed">
                Мы специализируемся на производстве и восстановлении режущего инструмента для промышленных предприятий.
                Наша команда профессионалов использует современное высокоточное оборудование для создания инструментов
                высочайшего качества.
              </p>
              <p className="text-muted-foreground leading-relaxed">
                За 15+ лет работы мы накопили огромный опыт и завоевали доверие сотен клиентов благодаря индивидуальному
                подходу и гарантии качества на всю продукцию.
              </p>
              <Button variant="outline" asChild>
                <Link href="/about">Подробнее о компании</Link>
              </Button>
            </div>
            <div className="relative">
              <img src="/placeholder-pijuw.png" alt="Производство ANAJ" className="rounded-lg shadow-lg w-full" />
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-16">
        <div className="container">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold font-heading">Наши услуги</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Полный спектр услуг по производству, восстановлению и обслуживанию режущего инструмента
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <Factory className="h-12 w-12 text-accent mx-auto mb-4" />
                <CardTitle className="font-heading">Производство инструмента</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Изготовление режущего инструмента по индивидуальным техническим требованиям
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <Wrench className="h-12 w-12 text-accent mx-auto mb-4" />
                <CardTitle className="font-heading">Восстановление инструмента</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>Профессиональное восстановление изношенного режущего инструмента</CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <Settings className="h-12 w-12 text-accent mx-auto mb-4" />
                <CardTitle className="font-heading">Индивидуальные заказы</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Разработка и изготовление специального инструмента под конкретные задачи
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <Users className="h-12 w-12 text-accent mx-auto mb-4" />
                <CardTitle className="font-heading">Консультация инженера</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>Техническая поддержка и консультации по выбору оптимального решения</CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Product Catalog Preview */}
      <section id="catalog" className="py-16 bg-secondary/30">
        <div className="container">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold font-heading">Каталог продукции</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Широкий ассортимент режущего инструмента для различных отраслей промышленности
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {[
              {
                name: "Фрезы концевые",
                description: "Высокоточные концевые фрезы для обработки металла",
                image: "end%20mills%20cutting%20tools",
              },
              {
                name: "Сверла спиральные",
                description: "Спиральные сверла различных диаметров и покрытий",
                image: "spiral%20drills%20industrial%20tools",
              },
              {
                name: "Резцы токарные",
                description: "Токарные резцы для точения различных материалов",
                image: "turning%20tools%20lathe%20cutters",
              },
              {
                name: "Развертки",
                description: "Машинные и ручные развертки для точной обработки отверстий",
                image: "reamers%20precision%20tools",
              },
              {
                name: "Метчики",
                description: "Метчики для нарезания внутренней резьбы",
                image: "taps%20threading%20tools",
              },
              {
                name: "Плашки",
                description: "Плашки для нарезания наружной резьбы",
                image: "dies%20external%20threading%20tools",
              },
            ].map((product, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow group">
                <div className="aspect-square overflow-hidden rounded-t-lg">
                  <img
                    src={`/abstract-geometric-shapes.png?height=300&width=300&query=${product.image}`}
                    alt={product.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <CardHeader>
                  <CardTitle className="font-heading">{product.name}</CardTitle>
                  <CardDescription>{product.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" className="w-full bg-transparent">
                    Подробнее
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <Button size="lg" className="bg-accent hover:bg-accent/90" asChild>
              <Link href="/catalog">Посмотреть весь каталог</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-16">
        <div className="container">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold font-heading">Почему выбирают нас</h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center space-y-4">
              <Clock className="h-16 w-16 text-accent mx-auto" />
              <h3 className="text-xl font-bold font-heading">Опыт 15+ лет</h3>
              <p className="text-muted-foreground">
                Многолетний опыт работы в сфере производства и восстановления режущего инструмента
              </p>
            </div>

            <div className="text-center space-y-4">
              <Award className="h-16 w-16 text-accent mx-auto" />
              <h3 className="text-xl font-bold font-heading">Высокоточное оборудование</h3>
              <p className="text-muted-foreground">
                Современные станки и технологии для производства инструмента высочайшего качества
              </p>
            </div>

            <div className="text-center space-y-4">
              <Shield className="h-16 w-16 text-accent mx-auto" />
              <h3 className="text-xl font-bold font-heading">Гарантия качества</h3>
              <p className="text-muted-foreground">
                Полная гарантия на всю продукцию и индивидуальный подход к каждому клиенту
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* News Section */}
      <section id="news" className="py-16 bg-secondary/30">
        <div className="container">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold font-heading">Последние новости</h2>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            {[
              {
                title: "Новое поступление концевых фрез",
                date: "15 декабря 2024",
                description: "Поступление новой партии высокоточных концевых фрез с покрытием TiAlN",
                image: "new%20end%20mills%20delivery",
                slug: "new-end-mills-tialn-coating",
              },
              {
                title: "Участие в выставке Металлообработка-2024",
                date: "10 декабря 2024",
                description: "Приглашаем посетить наш стенд на международной выставке металлообработки",
                image: "metalworking%20exhibition%202024",
                slug: "metalworking-exhibition-2024",
              },
              {
                title: "Расширение производственных мощностей",
                date: "5 декабря 2024",
                description: "Запуск нового участка по восстановлению сложного режущего инструмента",
                image: "production%20expansion%20factory",
                slug: "production-expansion-2024",
              },
            ].map((news, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <div className="aspect-video overflow-hidden rounded-t-lg">
                  <img
                    src={`/abstract-geometric-shapes.png?height=200&width=400&query=${news.image}`}
                    alt={news.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary">{news.date}</Badge>
                  </div>
                  <CardTitle className="font-heading">
                    <Link href={`/news/${news.slug}`} className="hover:text-accent transition-colors">
                      {news.title}
                    </Link>
                  </CardTitle>
                  <CardDescription>{news.description}</CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>

          <div className="text-center mt-8">
            <Button variant="outline" asChild>
              <Link href="/news">Все новости</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-primary text-primary-foreground py-12">
        <div className="container">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Factory className="h-6 w-6 text-accent" />
                <span className="text-xl font-bold font-heading">ANAJ.BY</span>
              </div>
              <p className="text-sm opacity-80">
                Производство и восстановление режущего инструмента высочайшего качества
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Контакты</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-accent" />
                  <span>+375 (29) 123-45-67</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-accent" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-accent" />
                  <span>г. Минск, ул. Промышленная, 15</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Услуги</h4>
              <div className="space-y-2 text-sm">
                <Link href="/services/manufacturing" className="block hover:text-accent transition-colors">
                  Производство инструмента
                </Link>
                <Link href="/services/restoration" className="block hover:text-accent transition-colors">
                  Восстановление инструмента
                </Link>
                <Link href="/services/custom" className="block hover:text-accent transition-colors">
                  Индивидуальные заказы
                </Link>
                <Link href="/services/consulting" className="block hover:text-accent transition-colors">
                  Консультации
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Информация</h4>
              <div className="space-y-2 text-sm">
                <Link href="/about" className="block hover:text-accent transition-colors">
                  О компании
                </Link>
                <Link href="/catalog" className="block hover:text-accent transition-colors">
                  Каталог продукции
                </Link>
                <Link href="/news" className="block hover:text-accent transition-colors">
                  Новости
                </Link>
                <a href="#contacts" className="block hover:text-accent transition-colors">
                  Контакты
                </a>
              </div>
            </div>
          </div>

          <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm opacity-80">
            <p>&copy; 2024 ANAJ.BY. Все права защищены.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
