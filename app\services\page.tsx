import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Phone, Mail, MapPin, Factory, Wrench, Settings, Users, CheckCircle, Clock, Award, Shield } from "lucide-react"
import Link from "next/link"

const services = [
  {
    id: "manufacturing",
    title: "Производство инструмента",
    icon: Factory,
    description:
      "Изготовление режущего инструмента по индивидуальным техническим требованиям с использованием современного высокоточного оборудования",
    features: [
      "Фрезы концевые всех типов и размеров",
      "Сверла спиральные с различными покрытиями",
      "Резцы токарные с твердосплавными пластинами",
      "Развертки машинные и ручные",
      "Метчики и плашки для нарезания резьбы",
      "Специальный инструмент по чертежам заказчика",
    ],
    process: [
      "Анализ технических требований",
      "Разработка конструкции инструмента",
      "Изготовление на высокоточном оборудовании",
      "Контроль качества и испытания",
      "Упаковка и отгрузка",
    ],
    advantages: [
      "Опыт производства 15+ лет",
      "Современное оборудование",
      "Контроль качества на каждом этапе",
      "Гарантия на всю продукцию",
    ],
    image: "manufacturing%20cutting%20tools%20factory",
  },
  {
    id: "restoration",
    title: "Восстановление инструмента",
    icon: Wrench,
    description:
      "Профессиональное восстановление изношенного режущего инструмента с восстановлением первоначальных характеристик и геометрии",
    features: [
      "Восстановление концевых фрез",
      "Переточка сверл и зенкеров",
      "Восстановление резцов и пластин",
      "Ремонт разверток и метчиков",
      "Восстановление специального инструмента",
      "Нанесение защитных покрытий",
    ],
    process: [
      "Диагностика состояния инструмента",
      "Определение технологии восстановления",
      "Переточка и восстановление геометрии",
      "Нанесение покрытий (при необходимости)",
      "Контроль качества и испытания",
    ],
    advantages: [
      "Экономия до 70% от стоимости нового",
      "Восстановление первоначальных свойств",
      "Быстрые сроки выполнения",
      "Гарантия качества работ",
    ],
    image: "tool%20restoration%20sharpening%20service",
  },
  {
    id: "custom",
    title: "Индивидуальные заказы",
    icon: Settings,
    description:
      "Разработка и изготовление специального режущего инструмента под конкретные технологические задачи заказчика",
    features: [
      "Проектирование по техническому заданию",
      "Изготовление опытных образцов",
      "Специальные геометрии и покрытия",
      "Нестандартные размеры и формы",
      "Комплексные технологические решения",
      "Техническое сопровождение внедрения",
    ],
    process: [
      "Анализ технологической задачи",
      "Разработка технического решения",
      "Согласование конструкции с заказчиком",
      "Изготовление и испытания",
      "Внедрение и техническая поддержка",
    ],
    advantages: [
      "Индивидуальный подход к каждой задаче",
      "Оптимизация технологических процессов",
      "Повышение производительности",
      "Полное техническое сопровождение",
    ],
    image: "custom%20cutting%20tools%20design",
  },
  {
    id: "consulting",
    title: "Консультация инженера",
    icon: Users,
    description:
      "Техническая поддержка и консультации по выбору оптимального режущего инструмента и технологических решений",
    features: [
      "Подбор оптимального инструмента",
      "Расчет режимов резания",
      "Анализ технологических процессов",
      "Рекомендации по повышению эффективности",
      "Обучение персонала заказчика",
      "Техническая поддержка 24/7",
    ],
    process: [
      "Анализ производственных задач",
      "Изучение технологических процессов",
      "Разработка рекомендаций",
      "Презентация решений заказчику",
      "Сопровождение внедрения",
    ],
    advantages: [
      "Экспертные знания и опыт",
      "Комплексный подход к решению задач",
      "Повышение эффективности производства",
      "Снижение производственных затрат",
    ],
    image: "engineering%20consultation%20technical%20support",
  },
]

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Factory className="h-8 w-8 text-accent" />
            <span className="text-2xl font-bold font-heading">ANAJ.BY</span>
          </Link>

          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/" className="text-sm font-medium hover:text-accent transition-colors">
              Главная
            </Link>
            <Link href="/catalog" className="text-sm font-medium hover:text-accent transition-colors">
              Каталог
            </Link>
            <Link href="/services" className="text-sm font-medium text-accent">
              Услуги
            </Link>
            <Link href="/about" className="text-sm font-medium hover:text-accent transition-colors">
              О компании
            </Link>
            <Link href="/news" className="text-sm font-medium hover:text-accent transition-colors">
              Новости
            </Link>
            <Link href="/contacts" className="text-sm font-medium hover:text-accent transition-colors">
              Контакты
            </Link>
          </nav>

          <Button className="bg-accent hover:bg-accent/90">
            <Phone className="mr-2 h-4 w-4" />
            Заказать звонок
          </Button>
        </div>
      </header>

      {/* Page Header */}
      <section className="py-12 bg-secondary/30">
        <div className="container">
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold font-heading">Наши услуги</h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Полный спектр услуг по производству, восстановлению и техническому сопровождению режущего инструмента
            </p>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16">
        <div className="container">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {services.map((service) => {
              const IconComponent = service.icon
              return (
                <Card key={service.id} className="text-center hover:shadow-lg transition-shadow group">
                  <CardHeader>
                    <IconComponent className="h-12 w-12 text-accent mx-auto mb-4 group-hover:scale-110 transition-transform" />
                    <CardTitle className="font-heading">{service.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="mb-4">{service.description}</CardDescription>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/services/${service.id}`}>Подробнее</Link>
                    </Button>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Detailed Services */}
      <section className="py-16 bg-secondary/30">
        <div className="container space-y-16">
          {services.map((service, index) => {
            const IconComponent = service.icon
            const isEven = index % 2 === 0

            return (
              <div
                key={service.id}
                className={`grid lg:grid-cols-2 gap-12 items-center ${!isEven ? "lg:grid-flow-col-dense" : ""}`}
              >
                <div className={`space-y-6 ${!isEven ? "lg:col-start-2" : ""}`}>
                  <div className="flex items-center space-x-3">
                    <IconComponent className="h-8 w-8 text-accent" />
                    <h2 className="text-3xl font-bold font-heading">{service.title}</h2>
                  </div>

                  <p className="text-muted-foreground leading-relaxed">{service.description}</p>

                  <div className="space-y-4">
                    <h3 className="text-xl font-bold font-heading">Что мы предлагаем:</h3>
                    <div className="grid sm:grid-cols-2 gap-2">
                      {service.features.slice(0, 4).map((feature, idx) => (
                        <div key={idx} className="flex items-start space-x-2">
                          <CheckCircle className="h-4 w-4 text-accent mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Button className="bg-accent hover:bg-accent/90" asChild>
                    <Link href={`/services/${service.id}`}>Узнать подробнее</Link>
                  </Button>
                </div>

                <div className={`relative ${!isEven ? "lg:col-start-1 lg:row-start-1" : ""}`}>
                  <img
                    src={`/abstract-geometric-shapes.png?height=400&width=600&query=${service.image}`}
                    alt={service.title}
                    className="rounded-lg shadow-lg w-full"
                  />
                </div>
              </div>
            )
          })}
        </div>
      </section>

      {/* Why Choose Our Services */}
      <section className="py-16">
        <div className="container">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold font-heading">Почему выбирают наши услуги</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Мы предоставляем комплексные решения с гарантией качества и полным техническим сопровождением
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center space-y-4">
              <Clock className="h-12 w-12 text-accent mx-auto" />
              <h3 className="text-lg font-bold font-heading">Быстрые сроки</h3>
              <p className="text-muted-foreground text-sm">
                Оперативное выполнение заказов благодаря отлаженным процессам
              </p>
            </div>

            <div className="text-center space-y-4">
              <Award className="h-12 w-12 text-accent mx-auto" />
              <h3 className="text-lg font-bold font-heading">Высокое качество</h3>
              <p className="text-muted-foreground text-sm">
                Контроль качества на каждом этапе производства и восстановления
              </p>
            </div>

            <div className="text-center space-y-4">
              <Shield className="h-12 w-12 text-accent mx-auto" />
              <h3 className="text-lg font-bold font-heading">Гарантия</h3>
              <p className="text-muted-foreground text-sm">
                Полная гарантия на все виды работ и изготовленную продукцию
              </p>
            </div>

            <div className="text-center space-y-4">
              <Users className="h-12 w-12 text-accent mx-auto" />
              <h3 className="text-lg font-bold font-heading">Экспертность</h3>
              <p className="text-muted-foreground text-sm">Команда опытных инженеров и технических специалистов</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-secondary/30">
        <div className="container text-center space-y-6">
          <h2 className="text-3xl font-bold font-heading">Готовы обсудить ваш проект?</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Свяжитесь с нашими специалистами для получения консультации и расчета стоимости услуг
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-accent hover:bg-accent/90">
              Получить консультацию
            </Button>
            <Button size="lg" variant="outline">
              <Phone className="mr-2 h-4 w-4" />
              Заказать звонок
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-primary text-primary-foreground py-12">
        <div className="container">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Factory className="h-6 w-6 text-accent" />
                <span className="text-xl font-bold font-heading">ANAJ.BY</span>
              </div>
              <p className="text-sm opacity-80">
                Производство и восстановление режущего инструмента высочайшего качества
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Контакты</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-accent" />
                  <span>+375 (29) 123-45-67</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-accent" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-accent" />
                  <span>г. Минск, ул. Промышленная, 15</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Услуги</h4>
              <div className="space-y-2 text-sm">
                <Link href="/services/manufacturing" className="block hover:text-accent transition-colors">
                  Производство инструмента
                </Link>
                <Link href="/services/restoration" className="block hover:text-accent transition-colors">
                  Восстановление инструмента
                </Link>
                <Link href="/services/custom" className="block hover:text-accent transition-colors">
                  Индивидуальные заказы
                </Link>
                <Link href="/services/consulting" className="block hover:text-accent transition-colors">
                  Консультации
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-bold font-heading">Информация</h4>
              <div className="space-y-2 text-sm">
                <Link href="/about" className="block hover:text-accent transition-colors">
                  О компании
                </Link>
                <Link href="/catalog" className="block hover:text-accent transition-colors">
                  Каталог продукции
                </Link>
                <Link href="/news" className="block hover:text-accent transition-colors">
                  Новости
                </Link>
                <Link href="/contacts" className="block hover:text-accent transition-colors">
                  Контакты
                </Link>
              </div>
            </div>
          </div>

          <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm opacity-80">
            <p>&copy; 2024 ANAJ.BY. Все права защищены.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
