@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0); /* White background */
  --foreground: oklch(0.35 0 0); /* Dark gray text (#4b5563) */
  --card: oklch(1 0 0); /* White cards */
  --card-foreground: oklch(0.35 0 0); /* Dark gray card text */
  --popover: oklch(1 0 0); /* White popover */
  --popover-foreground: oklch(0.35 0 0); /* Dark gray popover text */
  --primary: oklch(0.35 0 0); /* Dark gray primary */
  --primary-foreground: oklch(1 0 0); /* White text on primary */
  --secondary: oklch(0.92 0 0); /* Light gray secondary (#e5e7eb) */
  --secondary-foreground: oklch(0.35 0 0); /* Dark gray text on secondary */
  --muted: oklch(0.92 0 0); /* Light gray muted */
  --muted-foreground: oklch(0.35 0 0); /* Dark gray muted text */
  --accent: oklch(0.65 0.15 45); /* Orange accent (#ea580c) */
  --accent-foreground: oklch(1 0 0); /* White text on accent */
  --destructive: oklch(0.7 0.15 15); /* Red destructive */
  --destructive-foreground: oklch(1 0 0); /* White text on destructive */
  --border: oklch(0.92 0 0); /* Light gray border */
  --input: oklch(1 0 0); /* White input background */
  --ring: oklch(0.65 0.15 45 / 0.4); /* Orange focus ring */
  --chart-1: oklch(0.35 0 0); /* Dark gray chart */
  --chart-2: oklch(0.65 0.15 45); /* Orange chart */
  --chart-3: oklch(0.92 0 0); /* Light gray chart */
  --chart-4: oklch(1 0 0); /* White chart */
  --chart-5: oklch(0.35 0 0); /* Dark gray chart */
  --radius: 0.25rem; /* Minimal corner radius for industrial look */
  --sidebar: oklch(1 0 0); /* White sidebar */
  --sidebar-foreground: oklch(0.35 0 0); /* Dark gray sidebar text */
  --sidebar-primary: oklch(0.35 0 0); /* Dark gray sidebar primary */
  --sidebar-primary-foreground: oklch(1 0 0); /* White text on sidebar primary */
  --sidebar-accent: oklch(0.65 0.15 45); /* Orange sidebar accent */
  --sidebar-accent-foreground: oklch(1 0 0); /* White text on sidebar accent */
  --sidebar-border: oklch(0.92 0 0); /* Light gray sidebar border */
  --sidebar-ring: oklch(0.65 0.15 45 / 0.4); /* Orange sidebar focus ring */
}

.dark {
  --background: oklch(0.15 0 0); /* Dark background */
  --foreground: oklch(0.92 0 0); /* Light gray text */
  --card: oklch(0.18 0 0); /* Dark card */
  --card-foreground: oklch(0.92 0 0); /* Light gray card text */
  --popover: oklch(0.18 0 0); /* Dark popover */
  --popover-foreground: oklch(0.92 0 0); /* Light gray popover text */
  --primary: oklch(0.92 0 0); /* Light gray primary */
  --primary-foreground: oklch(0.15 0 0); /* Dark text on primary */
  --secondary: oklch(0.25 0 0); /* Dark gray secondary */
  --secondary-foreground: oklch(0.92 0 0); /* Light gray text on secondary */
  --muted: oklch(0.25 0 0); /* Dark gray muted */
  --muted-foreground: oklch(0.7 0 0); /* Medium gray muted text */
  --accent: oklch(0.65 0.15 45); /* Orange accent (same) */
  --accent-foreground: oklch(1 0 0); /* White text on accent */
  --destructive: oklch(0.6 0.2 15); /* Red destructive */
  --destructive-foreground: oklch(1 0 0); /* White text on destructive */
  --border: oklch(0.25 0 0); /* Dark gray border */
  --input: oklch(0.25 0 0); /* Dark gray input */
  --ring: oklch(0.65 0.15 45 / 0.4); /* Orange focus ring */
  --chart-1: oklch(0.92 0 0); /* Light gray chart */
  --chart-2: oklch(0.65 0.15 45); /* Orange chart */
  --chart-3: oklch(0.25 0 0); /* Dark gray chart */
  --chart-4: oklch(0.18 0 0); /* Dark chart */
  --chart-5: oklch(0.92 0 0); /* Light gray chart */
  --sidebar: oklch(0.18 0 0); /* Dark sidebar */
  --sidebar-foreground: oklch(0.92 0 0); /* Light gray sidebar text */
  --sidebar-primary: oklch(0.65 0.15 45); /* Orange sidebar primary */
  --sidebar-primary-foreground: oklch(1 0 0); /* White text on sidebar primary */
  --sidebar-accent: oklch(0.25 0 0); /* Dark gray sidebar accent */
  --sidebar-accent-foreground: oklch(0.92 0 0); /* Light gray text on sidebar accent */
  --sidebar-border: oklch(0.25 0 0); /* Dark gray sidebar border */
  --sidebar-ring: oklch(0.65 0.15 45 / 0.4); /* Orange sidebar focus ring */
}

@theme inline {
  /* Updated font variables to use Work Sans and Open Sans */
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-work-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
